<?php
// Configuración
$config = [
    'api_key' => '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',
    'sender_email' => '<EMAIL>',
    'customer_service' => '<EMAIL>',
    'company_name' => 'Digital Solution Department',
    'logo_url' => 'https://digitalsolutionsdepartment.com/img_DSD/logo-DSD.png'
];

// Función para enviar email
function send_brevo_email($data, $to_customer = false) {
    global $config;
    
    $email_data = [
        "sender" => ["name" => $config['company_name'], "email" => $to_customer ? $config['customer_service'] : $config['sender_email']],
        "to" => [["email" => $to_customer ? $data['email'] : $config['customer_service'], "name" => $to_customer ? $data['name'] : $config['company_name']]],
        "subject" => ($to_customer ? "Appointment Confirmation" : "New Appointment Request") . " - " . $config['company_name'],
        "htmlContent" => get_email_template($data, $to_customer)
    ];
    
    if (!$to_customer) $email_data["cc"] = [["email" => $config['customer_service'], "name" => 'Backup']];
    
    $response = wp_remote_post('https://api.brevo.com/v3/smtp/email', [
        'headers' => ['accept' => 'application/json', 'api-key' => $config['api_key'], 'content-type' => 'application/json'],
        'body' => json_encode($email_data),
        'timeout' => 30
    ]);
    
    return !is_wp_error($response) && wp_remote_retrieve_response_code($response) < 300;
}

// Plantilla de email
function get_email_template($data, $to_customer = false) {
    global $config;
    
    $title = $to_customer ? "Appointment Confirmation" : "New Appointment Request";
    $subtitle = $to_customer ? "Thank you for scheduling an appointment with us. Below are your appointment details:" : "A new appointment request has been received";
    
    $rows = $to_customer ? 
        '<tr style="background-color: #f5f5f5;"><td style="padding: 10px; font-weight: bold;">Name</td><td style="padding: 10px;">' . esc_html($data['name']) . '</td></tr>
         <tr style="background-color: #f5f5f5;"><td style="padding: 10px; font-weight: bold;">Date</td><td style="padding: 10px;">' . esc_html($data['date']) . '</td></tr>' :
        '<tr style="background-color: #f5f5f5;"><td style="padding: 10px; font-weight: bold;">Name</td><td style="padding: 10px;">' . esc_html($data['name']) . '</td></tr>
         <tr style="background-color: #f5f5f5;"><td style="padding: 10px; font-weight: bold;">Email</td><td style="padding: 10px;">' . esc_html($data['email']) . '</td></tr>
         <tr style="background-color: #f5f5f5;"><td style="padding: 10px; font-weight: bold;">Date</td><td style="padding: 10px;">' . esc_html($data['date']) . '</td></tr>
         <tr style="background-color: #f5f5f5;"><td style="padding: 10px; font-weight: bold;">Message</td><td style="padding: 10px;">' . nl2br(esc_html($data['message'])) . '</td></tr>';
    
    $footer = $to_customer ? '<p style="font-size: 15px; margin-top: 30px; color: #555;">If you need to make any changes, feel free to contact us at <a href="mailto:' . $config['customer_service'] . '">' . $config['customer_service'] . '</a>.</p><p style="font-size: 15px; margin-top: 20px; color: #555;">Best regards,<br><strong>' . $config['company_name'] . '</strong></p>' : '';
    
    return '<html><body><div style="font-family: Arial, sans-serif; padding: 30px; background-color: #ffffff; max-width: 600px; margin: auto; border: 1px solid #e0e0e0;">
        <div style="text-align: center; margin-bottom: 30px;"><img src="' . esc_url($config['logo_url']) . '" alt="' . esc_attr($config['company_name']) . '" style="max-height: 180px;"></div>
        <h2 style="text-align: center; font-weight: normal;">' . $title . '</h2>
        <p style="text-align: center; font-size: 16px; color: #555;">' . $subtitle . '</p>
        <table style="width: 100%; border-collapse: collapse; font-size: 15px; margin-top: 30px;">' . $rows . '</table>
        ' . $footer . '</div></body></html>';
}

// Handler AJAX
function handle_appointment_ajax() {
    if (!wp_verify_nonce($_POST['nonce'], 'appointment_nonce')) wp_die('Security check failed');
    
    $required = ['name', 'email', 'date', 'message'];
    foreach ($required as $field) {
        if (empty($_POST[$field])) wp_send_json_error("Field '$field' is required");
    }
    
    $data = [
        'name' => sanitize_text_field($_POST['name']),
        'email' => sanitize_email($_POST['email']),
        'date' => sanitize_text_field($_POST['date']),
        'message' => sanitize_textarea_field($_POST['message']),
        'confirmation' => isset($_POST['confirmation']) ? sanitize_text_field($_POST['confirmation']) : ''
    ];
    
    if (!is_email($data['email'])) wp_send_json_error('Invalid email format');
    
    $company_sent = send_brevo_email($data, false);
    $customer_sent = $data['confirmation'] !== 'confirmation' || send_brevo_email($data, true);
    
    wp_send_json($company_sent && $customer_sent ? ['success' => true, 'data' => 'Emails sent successfully'] : ['success' => false, 'data' => 'Error sending emails']);
}

// Hooks
add_action('wp_ajax_send_appointment_email', 'handle_appointment_ajax');
add_action('wp_ajax_nopriv_send_appointment_email', 'handle_appointment_ajax');
add_action('wp_enqueue_scripts', function() {
    wp_enqueue_script('jquery');
    wp_localize_script('jquery', 'ajax_object', ['ajax_url' => admin_url('admin-ajax.php'), 'nonce' => wp_create_nonce('appointment_nonce')]);
});

// Shortcode para formulario
function appointment_form_shortcode($atts) {
    $atts = shortcode_atts(['show_confirmation' => 'true'], $atts);
    
    ob_start(); ?>
    <form id="appointment-form" method="post">
        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" required>
        </div>
        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required>
        </div>
        <div class="form-group">
            <label for="date">Preferred Date *</label>
            <input type="text" id="date" name="date" required>
        </div>
        <div class="form-group">
            <label for="message">Message *</label>
            <textarea id="message" name="message" required></textarea>
        </div>
        <?php if ($atts['show_confirmation'] === 'true'): ?>
        <div class="form-group">
            <label><input type="checkbox" name="confirmation" value="confirmation"> Send confirmation email to customer</label>
        </div>
        <?php endif; ?>
        <button type="submit">Send Request</button>
        <div id="form-message"></div>
    </form>
    
    <script>
    jQuery(document).ready(function($) {
        $('#appointment-form').on('submit', function(e) {
            e.preventDefault();
            var formData = $(this).serialize() + '&action=send_appointment_email&nonce=' + ajax_object.nonce;
            $('#form-message').html('Sending...');
            
            $.ajax({
                url: ajax_object.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    $('#form-message').html(response.success ? 
                        '<p style="color: green;">Email sent successfully!</p>' : 
                        '<p style="color: red;">Error: ' + response.data + '</p>');
                    if (response.success) $('#appointment-form')[0].reset();
                },
                error: function() {
                    $('#form-message').html('<p style="color: red;">An error occurred. Please try again.</p>');
                }
            });
        });
    });
    </script>
    
    <style>
    .form-group { margin-bottom: 15px; }
    .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
    .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    .form-group textarea { height: 100px; resize: vertical; }
    button[type="submit"] { background-color: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    button[type="submit"]:hover { background-color: #005a87; }
    </style>
    <?php
    return ob_get_clean();
}
add_shortcode('appointment_form', 'appointment_form_shortcode');
?>
