<?php 

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['name']) && !empty($_POST['email']) && !empty($_POST['date']) && !empty($_POST['message']) && !empty($_POST['confirmation']) && $_POST['confirmation'] === "confirmation") {

  $apiKey = '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';
  $email = $_POST['email'];
  $name= $_POST['name'];

  $data = [
    "sender" => [
      "name" => "Digital Solution Department",
      "email" => "<EMAIL>"               
    ],
    "to" => [
      [
        "email" => $email,
        "name" => $name
      ]
    ],
    "subject" => "Digital Solution Department request",
    "htmlContent" => '<html><body>
  <div style="font-family: Arial, sans-serif; padding: 30px; background-color: #ffffff; max-width: 600px; margin: auto; border: 1px solid #e0e0e0;">
    <div style="text-align: center; margin-bottom: 30px;">
      <img src="https://digitalsolutionsdepartment.com/img_DSD/logo-DSD.png" alt="Digital Solution Department" style="max-height: 180px;">
    </div>

    <h2 style="text-align: center; font-weight: normal;">Appointment Confirmation</h2>
    <p style="text-align: center; font-size: 16px; color: #555;">
      Thank you for scheduling an appointment with us. Below are your appointment details:
    </p>

    <table style="width: 100%; border-collapse: collapse; font-size: 15px; margin-top: 30px;">
      <tr style="background-color: #f5f5f5;">
        <td style="padding: 10px; font-weight: bold;">Name</td>
        <td style="padding: 10px;">' . htmlspecialchars($_POST['name']) . '</td>
      </tr>
      <tr style="background-color: #f5f5f5;">
        <td style="padding: 10px; font-weight: bold;">Date</td>
        <td style="padding: 10px;">' . htmlspecialchars($_POST['date']) . '</td>
      </tr>
    </table>

    <p style="font-size: 15px; margin-top: 30px; color: #555;">
      If you need to make any changes, feel free to contact us at 
      <a href="mailto:<EMAIL>"><EMAIL></a>.
    </p>

    <p style="font-size: 15px; margin-top: 20px; color: #555;">
      Best regards,<br>
      <strong>Digital Solution Department</strong>
    </p>
  </div>
</body></html>'
  ];

  $ch = curl_init();

  curl_setopt($ch, CURLOPT_URL, "https://api.brevo.com/v3/smtp/email");
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($ch, CURLOPT_POST, true);
  curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "accept: application/json",
    "api-key: $apiKey",
    "content-type: application/json"
  ]);
  curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

  $response = curl_exec($ch);

  if (curl_errno($ch)) {
    echo "Error al enviar: " . curl_error($ch);
  } else {
    echo "Correo enviado correctamente: $response";
  }

  curl_close($ch);

}

?>