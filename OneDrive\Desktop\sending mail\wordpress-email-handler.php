<?php
/**
 * WordPress Email Handler for Digital Solution Department
 * Handles appointment requests and sends confirmation emails
 * 
 * Usage: Add this code to your theme's functions.php or use as a snippet
 */

class DigitalSolutionEmailHandler {
    
    private $apiKey = '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';
    private $senderEmail = '<EMAIL>';
    private $customerServiceEmail = '<EMAIL>';
    private $companyName = 'Digital Solution Department';
    private $logoUrl = 'https://digitalsolutionsdepartment.com/img_DSD/logo-DSD.png';
    
    public function __construct() {
        add_action('wp_ajax_send_appointment_email', array($this, 'handle_appointment_request'));
        add_action('wp_ajax_nopriv_send_appointment_email', array($this, 'handle_appointment_request'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Enqueue necessary scripts for AJAX
     */
    public function enqueue_scripts() {
        wp_enqueue_script('jquery');
        wp_localize_script('jquery', 'ajax_object', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('appointment_nonce')
        ));
    }
    
    /**
     * Main handler for appointment requests
     */
    public function handle_appointment_request() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'appointment_nonce')) {
            wp_die('Security check failed');
        }
        
        // Validate required fields
        $required_fields = ['name', 'email', 'date', 'message'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                wp_send_json_error("Field '$field' is required");
                return;
            }
        }
        
        // Sanitize input data
        $data = array(
            'name' => sanitize_text_field($_POST['name']),
            'email' => sanitize_email($_POST['email']),
            'date' => sanitize_text_field($_POST['date']),
            'message' => sanitize_textarea_field($_POST['message']),
            'confirmation' => isset($_POST['confirmation']) ? sanitize_text_field($_POST['confirmation']) : ''
        );
        
        // Validate email format
        if (!is_email($data['email'])) {
            wp_send_json_error('Invalid email format');
            return;
        }
        
        try {
            // Send notification to company
            $company_result = $this->send_company_notification($data);
            
            // Send confirmation to customer if confirmation flag is set
            $customer_result = true;
            if ($data['confirmation'] === 'confirmation') {
                $customer_result = $this->send_customer_confirmation($data);
            }
            
            if ($company_result && $customer_result) {
                wp_send_json_success('Emails sent successfully');
            } else {
                wp_send_json_error('Error sending emails');
            }
            
        } catch (Exception $e) {
            error_log('Email sending error: ' . $e->getMessage());
            wp_send_json_error('An error occurred while sending emails');
        }
    }
    
    /**
     * Send notification email to company
     */
    private function send_company_notification($data) {
        $email_data = array(
            "sender" => array(
                "name" => $this->companyName,
                "email" => $this->senderEmail
            ),
            "to" => array(
                array(
                    "email" => $this->customerServiceEmail,
                    "name" => $this->companyName
                )
            ),
            "cc" => array(
                array(
                    "email" => $this->customerServiceEmail,
                    "name" => 'Backup'
                )
            ),
            "subject" => "New Appointment Request - " . $this->companyName,
            "htmlContent" => $this->get_company_email_template($data)
        );
        
        return $this->send_email($email_data);
    }
    
    /**
     * Send confirmation email to customer
     */
    private function send_customer_confirmation($data) {
        $email_data = array(
            "sender" => array(
                "name" => $this->companyName,
                "email" => $this->customerServiceEmail
            ),
            "to" => array(
                array(
                    "email" => $data['email'],
                    "name" => $data['name']
                )
            ),
            "subject" => "Appointment Confirmation - " . $this->companyName,
            "htmlContent" => $this->get_customer_email_template($data)
        );
        
        return $this->send_email($email_data);
    }
    
    /**
     * Send email using Brevo API
     */
    private function send_email($email_data) {
        $response = wp_remote_post('https://api.brevo.com/v3/smtp/email', array(
            'headers' => array(
                'accept' => 'application/json',
                'api-key' => $this->apiKey,
                'content-type' => 'application/json'
            ),
            'body' => json_encode($email_data),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            error_log('Email API Error: ' . $response->get_error_message());
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code >= 200 && $response_code < 300) {
            return true;
        } else {
            error_log('Email API Error - Code: ' . $response_code . ', Body: ' . $response_body);
            return false;
        }
    }
    
    /**
     * Get email template for company notification
     */
    private function get_company_email_template($data) {
        return '
        <html><body>
        <div style="font-family: Arial, sans-serif; padding: 30px; background-color: #ffffff; max-width: 600px; margin: auto; border: 1px solid #e0e0e0;">
            <div style="text-align: center; margin-bottom: 30px;">
                <img src="' . esc_url($this->logoUrl) . '" alt="' . esc_attr($this->companyName) . '" style="max-height: 180px;">
            </div>
            
            <h2 style="text-align: center; font-weight: normal;">New Appointment Request</h2>
            <p style="text-align: center; font-size: 16px; color: #555;">A new appointment request has been received</p>
            
            <p style="font-size: 16px; margin-top: 40px;"><strong>Details:</strong></p>
            
            <table style="width: 100%; border-collapse: collapse; font-size: 15px;">
                <tr style="background-color: #f5f5f5;">
                    <td style="padding: 10px; font-weight: bold;">Name</td>
                    <td style="padding: 10px;">' . esc_html($data['name']) . '</td>
                </tr>
                <tr style="background-color: #f5f5f5;">
                    <td style="padding: 10px; font-weight: bold;">Email</td>
                    <td style="padding: 10px;">' . esc_html($data['email']) . '</td>
                </tr>
                <tr style="background-color: #f5f5f5;">
                    <td style="padding: 10px; font-weight: bold;">Date</td>
                    <td style="padding: 10px;">' . esc_html($data['date']) . '</td>
                </tr>
                <tr style="background-color: #f5f5f5;">
                    <td style="padding: 10px; font-weight: bold; vertical-align: top;">Message</td>
                    <td style="padding: 10px;">' . nl2br(esc_html($data['message'])) . '</td>
                </tr>
            </table>
        </div>
        </body></html>';
    }
    
    /**
     * Get email template for customer confirmation
     */
    private function get_customer_email_template($data) {
        return '
        <html><body>
        <div style="font-family: Arial, sans-serif; padding: 30px; background-color: #ffffff; max-width: 600px; margin: auto; border: 1px solid #e0e0e0;">
            <div style="text-align: center; margin-bottom: 30px;">
                <img src="' . esc_url($this->logoUrl) . '" alt="' . esc_attr($this->companyName) . '" style="max-height: 180px;">
            </div>
            
            <h2 style="text-align: center; font-weight: normal;">Appointment Confirmation</h2>
            <p style="text-align: center; font-size: 16px; color: #555;">
                Thank you for scheduling an appointment with us. Below are your appointment details:
            </p>
            
            <table style="width: 100%; border-collapse: collapse; font-size: 15px; margin-top: 30px;">
                <tr style="background-color: #f5f5f5;">
                    <td style="padding: 10px; font-weight: bold;">Name</td>
                    <td style="padding: 10px;">' . esc_html($data['name']) . '</td>
                </tr>
                <tr style="background-color: #f5f5f5;">
                    <td style="padding: 10px; font-weight: bold;">Date</td>
                    <td style="padding: 10px;">' . esc_html($data['date']) . '</td>
                </tr>
            </table>
            
            <p style="font-size: 15px; margin-top: 30px; color: #555;">
                If you need to make any changes, feel free to contact us at 
                <a href="mailto:' . esc_attr($this->customerServiceEmail) . '">' . esc_html($this->customerServiceEmail) . '</a>.
            </p>
            
            <p style="font-size: 15px; margin-top: 20px; color: #555;">
                Best regards,<br>
                <strong>' . esc_html($this->companyName) . '</strong>
            </p>
        </div>
        </body></html>';
    }
}

// Initialize the email handler
new DigitalSolutionEmailHandler();

/**
 * Shortcode for appointment form
 * Usage: [appointment_form]
 */
function appointment_form_shortcode($atts) {
    $atts = shortcode_atts(array(
        'show_confirmation' => 'true'
    ), $atts);
    
    ob_start();
    ?>
    <form id="appointment-form" method="post">
        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="date">Preferred Date *</label>
            <input type="text" id="date" name="date" required>
        </div>
        
        <div class="form-group">
            <label for="message">Message *</label>
            <textarea id="message" name="message" required></textarea>
        </div>
        
        <?php if ($atts['show_confirmation'] === 'true'): ?>
        <div class="form-group">
            <label>
                <input type="checkbox" name="confirmation" value="confirmation">
                Send confirmation email to customer
            </label>
        </div>
        <?php endif; ?>
        
        <button type="submit">Send Request</button>
        <div id="form-message"></div>
    </form>
    
    <script>
    jQuery(document).ready(function($) {
        $('#appointment-form').on('submit', function(e) {
            e.preventDefault();
            
            var formData = $(this).serialize();
            formData += '&action=send_appointment_email&nonce=' + ajax_object.nonce;
            
            $('#form-message').html('Sending...');
            
            $.ajax({
                url: ajax_object.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        $('#form-message').html('<p style="color: green;">Email sent successfully!</p>');
                        $('#appointment-form')[0].reset();
                    } else {
                        $('#form-message').html('<p style="color: red;">Error: ' + response.data + '</p>');
                    }
                },
                error: function() {
                    $('#form-message').html('<p style="color: red;">An error occurred. Please try again.</p>');
                }
            });
        });
    });
    </script>
    
    <style>
    .form-group {
        margin-bottom: 15px;
    }
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .form-group textarea {
        height: 100px;
        resize: vertical;
    }
    button[type="submit"] {
        background-color: #007cba;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    button[type="submit"]:hover {
        background-color: #005a87;
    }
    </style>
    <?php
    return ob_get_clean();
}
add_shortcode('appointment_form', 'appointment_form_shortcode');
?>
